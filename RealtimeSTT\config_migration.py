"""
Configuration Migration Utility for RealtimeSTT Ollama Integration

This module provides utilities for migrating existing configurations to work with
the new Ollama integration while maintaining backward compatibility.
"""

import logging
import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime

logger = logging.getLogger("realtimestt.config_migration")


class ConfigMigration:
    """
    Handles configuration migration for RealtimeSTT Ollama integration.
    Ensures backward compatibility with existing configurations.
    """
    
    def __init__(self):
        self.migration_version = "1.0.0"
        self.supported_versions = ["0.x", "1.0.0"]
    
    def migrate_recorder_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate existing recorder configuration to support Ollama integration.
        
        Args:
            config: Existing recorder configuration
            
        Returns:
            Dict: Migrated configuration with Ollama support
        """
        migrated_config = config.copy()
        
        # Add Ollama parameters with backward-compatible defaults
        ollama_defaults = {
            'use_ollama': True,  # Default to local-first approach
            'ollama_model': 'gemma3:1b',
            'ollama_host': 'localhost:11434',
            'ollama_timeout': 30.0,
            'ollama_max_retries': 3,
            'ollama_auto_download': True,
            'fallback_to_external': True,  # Ensure backward compatibility
        }
        
        # Only add Ollama parameters if they don't already exist
        for key, value in ollama_defaults.items():
            if key not in migrated_config:
                migrated_config[key] = value
        
        # Handle model migration
        if 'model' in migrated_config:
            original_model = migrated_config['model']
            
            # If using a traditional Whisper model, keep it as fallback
            if self._is_whisper_model(original_model):
                # Keep the original model for fallback
                migrated_config['whisper_fallback_model'] = original_model
                # But default to Ollama for new local-first approach
                if migrated_config.get('use_ollama', True):
                    logger.info(f"Migrating from Whisper model '{original_model}' to Ollama model '{migrated_config['ollama_model']}'")
                    logger.info(f"Original model '{original_model}' will be used as fallback")
            else:
                # If it's already an Ollama model or custom model, keep it
                migrated_config['ollama_model'] = original_model
        
        # Ensure API keys are optional
        self._make_api_keys_optional(migrated_config)
        
        logger.info("Configuration migrated successfully for Ollama integration")
        return migrated_config
    
    def _is_whisper_model(self, model_name: str) -> bool:
        """
        Check if a model name refers to a traditional Whisper model.
        
        Args:
            model_name: Model name to check
            
        Returns:
            bool: True if it's a Whisper model
        """
        whisper_models = [
            'tiny', 'tiny.en', 'base', 'base.en', 'small', 'small.en',
            'medium', 'medium.en', 'large-v1', 'large-v2', 'large-v3'
        ]
        
        # Check for exact matches or HuggingFace model paths
        if model_name in whisper_models:
            return True
        
        # Check for HuggingFace model patterns
        if ('whisper' in model_name.lower() or 
            'faster-whisper' in model_name.lower() or
            model_name.startswith('deepdml/') or
            model_name.startswith('openai/')):
            return True
        
        return False
    
    def _make_api_keys_optional(self, config: Dict[str, Any]):
        """
        Ensure API keys are optional when using local Ollama models.
        
        Args:
            config: Configuration to modify
        """
        # Mark API keys as optional in the configuration
        api_key_fields = [
            'openai_api_key', 'azure_api_key', 'elevenlabs_api_key',
            'openai_key', 'azure_key', 'elevenlabs_key'
        ]
        
        for field in api_key_fields:
            if field in config:
                # Keep the key but mark it as optional
                config[f'{field}_optional'] = True
    
    def create_compatibility_config(self, 
                                  use_ollama: bool = True,
                                  preserve_external_apis: bool = True) -> Dict[str, Any]:
        """
        Create a configuration that maintains compatibility with existing setups.
        
        Args:
            use_ollama: Whether to enable Ollama by default
            preserve_external_apis: Whether to keep external API configurations
            
        Returns:
            Dict: Compatibility configuration
        """
        config = {
            # Core Ollama settings
            'use_ollama': use_ollama,
            'ollama_model': 'gemma3:1b',
            'ollama_host': 'localhost:11434',
            'ollama_timeout': 30.0,
            'ollama_max_retries': 3,
            'ollama_auto_download': True,
            'fallback_to_external': preserve_external_apis,
            
            # Backward compatibility settings
            'model': 'gemma3:1b' if use_ollama else 'large-v2',
            'realtime_model_type': 'tiny.en',  # Keep fast model for realtime
            'language': 'en',
            'enable_realtime_transcription': True,
            
            # Preserve existing VAD settings
            'silero_sensitivity': 0.4,
            'webrtc_sensitivity': 3,
            'post_speech_silence_duration': 0.6,
            'min_length_of_recording': 0.5,
            'min_gap_between_recordings': 0,
            
            # Preserve existing audio settings
            'buffer_size': 512,
            'sample_rate': 16000,
            'input_device_index': None,
            
            # Preserve existing processing settings
            'beam_size': 5,
            'beam_size_realtime': 3,
            'batch_size': 16,
            'realtime_batch_size': 16,
        }
        
        if preserve_external_apis:
            # Add placeholders for external API configurations
            config.update({
                'openai_api_key_optional': True,
                'azure_api_key_optional': True,
                'elevenlabs_api_key_optional': True,
            })
        
        return config
    
    def validate_migrated_config(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate a migrated configuration and return any warnings or issues.
        
        Args:
            config: Configuration to validate
            
        Returns:
            List[str]: List of validation warnings/issues
        """
        warnings = []
        
        # Check for required Ollama parameters
        required_ollama_params = [
            'use_ollama', 'ollama_model', 'ollama_host', 
            'ollama_timeout', 'fallback_to_external'
        ]
        
        for param in required_ollama_params:
            if param not in config:
                warnings.append(f"Missing Ollama parameter: {param}")
        
        # Check for conflicting settings
        if config.get('use_ollama', False) and not config.get('fallback_to_external', True):
            if not self._has_external_api_keys(config):
                warnings.append("Ollama enabled without fallback, but no external API keys configured")
        
        # Check model compatibility
        if 'model' in config and 'ollama_model' in config:
            if config['model'] != config['ollama_model'] and config.get('use_ollama', False):
                warnings.append(f"Model mismatch: 'model' is '{config['model']}' but 'ollama_model' is '{config['ollama_model']}'")
        
        return warnings
    
    def _has_external_api_keys(self, config: Dict[str, Any]) -> bool:
        """
        Check if configuration has external API keys configured.
        
        Args:
            config: Configuration to check
            
        Returns:
            bool: True if external API keys are present
        """
        api_key_fields = [
            'openai_api_key', 'azure_api_key', 'elevenlabs_api_key',
            'openai_key', 'azure_key', 'elevenlabs_key'
        ]
        
        for field in api_key_fields:
            if config.get(field) and not config.get(f'{field}_optional', False):
                return True
        
        return False
    
    def save_migration_log(self, 
                          original_config: Dict[str, Any], 
                          migrated_config: Dict[str, Any],
                          log_path: Optional[str] = None):
        """
        Save a migration log for debugging and rollback purposes.
        
        Args:
            original_config: Original configuration
            migrated_config: Migrated configuration
            log_path: Path to save the log (optional)
        """
        if not log_path:
            log_path = "realtimestt_migration.log"
        
        migration_log = {
            'migration_version': self.migration_version,
            'timestamp': str(datetime.now()),
            'original_config': original_config,
            'migrated_config': migrated_config,
            'changes': self._get_config_changes(original_config, migrated_config)
        }
        
        try:
            with open(log_path, 'w') as f:
                json.dump(migration_log, f, indent=2)
            logger.info(f"Migration log saved to: {log_path}")
        except Exception as e:
            logger.error(f"Failed to save migration log: {e}")
    
    def _get_config_changes(self, 
                           original: Dict[str, Any], 
                           migrated: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a summary of changes made during migration.
        
        Args:
            original: Original configuration
            migrated: Migrated configuration
            
        Returns:
            Dict: Summary of changes
        """
        changes = {
            'added': {},
            'modified': {},
            'removed': {}
        }
        
        # Find added keys
        for key in migrated:
            if key not in original:
                changes['added'][key] = migrated[key]
        
        # Find modified keys
        for key in original:
            if key in migrated and original[key] != migrated[key]:
                changes['modified'][key] = {
                    'old': original[key],
                    'new': migrated[key]
                }
        
        # Find removed keys
        for key in original:
            if key not in migrated:
                changes['removed'][key] = original[key]
        
        return changes
