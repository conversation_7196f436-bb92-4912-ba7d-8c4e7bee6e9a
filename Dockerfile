FROM nvidia/cuda:12.4.1-runtime-ubuntu22.04 as gpu

WORKDIR /app

RUN apt-get update -y && \
  apt-get install -y python3 python3-pip libcudnn8 libcudnn8-dev libcublas-12-4 portaudio19-dev curl

# Install Ollama
RUN curl -fsSL https://ollama.ai/install.sh | sh

RUN pip3 install torch==2.3.0 torchaudio==2.3.0

COPY requirements-gpu.txt /app/requirements-gpu.txt
RUN pip3 install -r /app/requirements-gpu.txt

# Install Ollama Python client
RUN pip3 install ollama requests

RUN mkdir example_browserclient
COPY example_browserclient/server.py /app/example_browserclient/server.py
COPY RealtimeSTT /app/RealtimeSTT

# Create Ollama data directory
RUN mkdir -p /root/.ollama

EXPOSE 9001 11434
ENV PYTHONPATH "${PYTHONPATH}:/app"
ENV OLLAMA_HOST=0.0.0.0:11434
RUN export PYTHONPATH="${PYTHONPATH}:/app"

# Create startup script that starts Ollama and the application
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

CMD ["/app/docker-entrypoint.sh"]

# --------------------------------------------

FROM ubuntu:22.04 as cpu

WORKDIR /app

RUN apt-get update -y && \
  apt-get install -y python3 python3-pip portaudio19-dev curl

# Install Ollama
RUN curl -fsSL https://ollama.ai/install.sh | sh

RUN pip3 install torch==2.3.0 torchaudio==2.3.0

COPY requirements.txt /app/requirements.txt
RUN pip3 install -r /app/requirements.txt

# Install Ollama Python client
RUN pip3 install ollama requests

# Copy application files
COPY example_browserclient/server.py /app/example_browserclient/server.py
COPY RealtimeSTT /app/RealtimeSTT

# Create Ollama data directory
RUN mkdir -p /root/.ollama

EXPOSE 9001 11434
ENV PYTHONPATH "${PYTHONPATH}:/app"
ENV OLLAMA_HOST=0.0.0.0:11434
RUN export PYTHONPATH="${PYTHONPATH}:/app"

# Create startup script that starts Ollama and the application
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

CMD ["/app/docker-entrypoint.sh"]
