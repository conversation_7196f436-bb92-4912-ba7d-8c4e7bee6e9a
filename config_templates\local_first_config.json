{
  "description": "Local-first configuration template for RealtimeSTT with Ollama integration",
  "version": "1.0.0",
  "
  
  "core_settings": {
    "model": "gemma3:1b",
    "language": "en",
    "use_microphone": true,
    "spinner": true
  },
  
  "ollama_integration": {
    "use_ollama": true,
    "ollama_model": "gemma3:1b",
    "ollama_host": "localhost:11434",
    "ollama_timeout": 30.0,
    "ollama_max_retries": 3,
    "ollama_auto_download": true,
    "fallback_to_external": true
  },
  
  "fallback_configuration": {
    "whisper_fallback_model": "base",
    "realtime_model_type": "tiny.en",
    "enable_realtime_transcription": true
  },
  
  "voice_activity_detection": {
    "silero_sensitivity": 0.4,
    "webrtc_sensitivity": 3,
    "post_speech_silence_duration": 0.6,
    "min_length_of_recording": 0.5,
    "min_gap_between_recordings": 0
  },
  
  "audio_settings": {
    "sample_rate": 16000,
    "buffer_size": 512,
    "input_device_index": null
  },
  
  "processing_settings": {
    "beam_size": 5,
    "beam_size_realtime": 3,
    "batch_size": 16,
    "realtime_batch_size": 16,
    "realtime_processing_pause": 0.02
  },
  
  "advanced_settings": {
    "early_transcription_on_silence": 0,
    "silero_deactivity_detection": true,
    "handle_buffer_overflow": false,
    "faster_whisper_vad_filter": true,
    "normalize_audio": false
  },
  
  "logging": {
    "use_extended_logging": false,
    "no_log_file": true,
    "level": "WARNING"
  },
  
  "external_apis": {
    "description": "Optional external API configurations (only used as fallback)",
    "openai_api_key": null,
    "azure_speech_key": null,
    "azure_speech_region": null,
    "elevenlabs_api_key": null
  },
  
  "environment_variables": {
    "OLLAMA_HOST": "localhost:11434",
    "OLLAMA_MODEL": "gemma3:1b",
    "USE_OLLAMA": "true"
  },
  
  "usage_examples": {
    "basic_usage": {
      "description": "Basic usage with local Ollama",
      "code": "recorder = AudioToTextRecorder(**config['core_settings'], **config['ollama_integration'])"
    },
    "with_fallback": {
      "description": "Usage with automatic fallback",
      "code": "recorder = AudioToTextRecorder(**config['core_settings'], **config['ollama_integration'], **config['fallback_configuration'])"
    },
    "custom_model": {
      "description": "Using a different Ollama model",
      "code": "config['ollama_integration']['ollama_model'] = 'llama3.2:1b'"
    }
  },
  
  "troubleshooting": {
    "ollama_not_running": {
      "problem": "Ollama server not accessible",
      "solutions": [
        "Start Ollama server: 'ollama serve'",
        "Check if port 11434 is available",
        "Verify Ollama installation"
      ]
    },
    "model_not_found": {
      "problem": "Specified model not available",
      "solutions": [
        "Pull the model: 'ollama pull gemma3:1b'",
        "Check available models: 'ollama list'",
        "Enable auto-download: 'ollama_auto_download': true"
      ]
    },
    "performance_issues": {
      "problem": "Slow transcription performance",
      "solutions": [
        "Use smaller model: 'gemma3:1b' instead of larger models",
        "Adjust batch_size and beam_size",
        "Enable GPU acceleration if available"
      ]
    }
  },
  
  "model_recommendations": {
    "fast_and_light": {
      "model": "gemma3:1b",
      "description": "Best balance of speed and accuracy for most use cases",
      "memory_usage": "~1.5GB",
      "speed": "Fast"
    },
    "higher_accuracy": {
      "model": "llama3.2:3b",
      "description": "Better accuracy for complex transcriptions",
      "memory_usage": "~3GB",
      "speed": "Medium"
    },
    "minimal_resources": {
      "model": "tinyllama:1.1b",
      "description": "Minimal resource usage for constrained environments",
      "memory_usage": "~1GB",
      "speed": "Very Fast"
    }
  }
}
