#!/usr/bin/env python3
"""
Ollama Local Example for RealtimeSTT

This example demonstrates how to use RealtimeSTT with local Ollama models,
showcasing the local-first approach with automatic fallback capabilities.

Features:
- Local Ollama model integration (gemma3:1b by default)
- Automatic model downloading if not available
- Graceful fallback to Whisper models if Ollama unavailable
- Real-time transcription with local processing
- No external API dependencies required

Requirements:
- Ollama installed and running (ollama.ai)
- RealtimeSTT with Ollama integration
- gemma3:1b model (will be auto-downloaded)

Usage:
    python ollama_local_example.py
"""

import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path to import RealtimeSTT
sys.path.append(str(Path(__file__).parent.parent))

try:
    from RealtimeSTT import AudioToTextRecorder
    print("✓ RealtimeSTT imported successfully")
except ImportError as e:
    print(f"✗ Failed to import RealtimeSTT: {e}")
    print("Please install RealtimeSTT: pip install RealtimeSTT")
    sys.exit(1)

def check_ollama_availability():
    """Check if Ollama server is running and accessible."""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✓ Ollama server is running with {len(models)} models available")
            return True
        else:
            print(f"✗ Ollama server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Ollama server not accessible: {e}")
        return False

def main():
    print("=" * 60)
    print("RealtimeSTT Ollama Local Example")
    print("=" * 60)
    print("This example uses local Ollama models for transcription")
    print("No external API keys required!")
    print()
    
    # Check Ollama availability
    ollama_available = check_ollama_availability()
    
    if not ollama_available:
        print()
        print("Ollama Setup Instructions:")
        print("1. Install Ollama from https://ollama.ai")
        print("2. Start Ollama: 'ollama serve'")
        print("3. Pull gemma3:1b model: 'ollama pull gemma3:1b'")
        print()
        print("Continuing with fallback to Whisper models...")
        print()
    
    # Configure the recorder with Ollama integration
    recorder_config = {
        # Core settings
        'model': 'gemma3:1b',  # Primary Ollama model
        'language': 'en',
        'spinner': True,
        'use_microphone': True,
        
        # Ollama Integration
        'use_ollama': True,
        'ollama_model': 'gemma3:1b',
        'ollama_host': 'localhost:11434',
        'ollama_timeout': 30.0,
        'ollama_max_retries': 3,
        'ollama_auto_download': True,  # Auto-download model if missing
        'fallback_to_external': True,  # Fallback to Whisper if Ollama fails
        
        # Fallback Whisper model
        'whisper_fallback_model': 'base',  # Smaller model for faster fallback
        
        # Real-time transcription settings
        'enable_realtime_transcription': True,
        'realtime_model_type': 'tiny.en',  # Fast model for real-time
        'realtime_processing_pause': 0.02,
        
        # Voice Activity Detection
        'silero_sensitivity': 0.4,
        'webrtc_sensitivity': 3,
        'post_speech_silence_duration': 0.6,
        'min_length_of_recording': 0.5,
        'min_gap_between_recordings': 0,
        
        # Audio settings
        'sample_rate': 16000,
        'buffer_size': 512,
        
        # Processing settings
        'beam_size': 5,
        'beam_size_realtime': 3,
        'batch_size': 16,
        
        # Logging
        'use_extended_logging': False,
        'no_log_file': True,
    }
    
    print("Initializing RealtimeSTT with Ollama integration...")
    print()
    print("Configuration:")
    print(f"  Primary Model: {recorder_config['ollama_model']}")
    print(f"  Ollama Host: {recorder_config['ollama_host']}")
    print(f"  Auto-download: {recorder_config['ollama_auto_download']}")
    print(f"  Fallback Model: {recorder_config['whisper_fallback_model']}")
    print(f"  Real-time Model: {recorder_config['realtime_model_type']}")
    print()
    
    try:
        # Initialize the recorder
        recorder = AudioToTextRecorder(**recorder_config)
        
        # Check which model is actually being used
        if hasattr(recorder, 'is_ollama_available') and recorder.is_ollama_available():
            print("✓ Using Ollama for transcription")
            available_models = recorder.get_available_ollama_models()
            if available_models:
                print(f"  Available Ollama models: {', '.join(available_models)}")
        else:
            print("⚠ Using Whisper fallback model")
        
        print()
        print("=" * 60)
        print("Ready for transcription!")
        print("Speak into your microphone. Press Ctrl+C to stop.")
        print("=" * 60)
        print()
        
        # Start transcription loop
        sentence_count = 0
        while True:
            try:
                # Get transcription
                text = recorder.text()
                
                if text.strip():
                    sentence_count += 1
                    print(f"[{sentence_count:03d}] {text}")
                    
                    # Demonstrate model switching (optional)
                    if sentence_count == 5 and hasattr(recorder, 'switch_to_ollama_model'):
                        print("\n--- Demonstrating model switching ---")
                        if recorder.switch_to_ollama_model('gemma3:1b'):
                            print("✓ Switched to gemma3:1b model")
                        else:
                            print("⚠ Model switch failed, continuing with current model")
                        print("--- Continuing transcription ---\n")
                
            except KeyboardInterrupt:
                print("\n\nStopping transcription...")
                break
            except Exception as e:
                print(f"\nError during transcription: {e}")
                print("Continuing...")
                continue
    
    except Exception as e:
        print(f"Failed to initialize recorder: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure Ollama is installed and running")
        print("2. Check that the gemma3:1b model is available")
        print("3. Verify microphone permissions")
        return 1
    
    finally:
        # Cleanup
        if 'recorder' in locals():
            try:
                recorder.shutdown()
                print("✓ Recorder shutdown complete")
            except:
                pass
    
    print("Example completed successfully!")
    return 0

if __name__ == "__main__":
    # Enable debug logging if requested
    if "--debug" in sys.argv:
        logging.basicConfig(level=logging.DEBUG)
        print("Debug logging enabled")
    
    sys.exit(main())
