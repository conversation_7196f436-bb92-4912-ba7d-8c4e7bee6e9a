import RealtimeSTT, RealtimeTTS
import openai, os

if __name__ == '__main__':
    # Ollama Integration: Use local Ollama by default, fallback to OpenAI if needed
    use_ollama = True

    # Initialize OpenAI client for Ollama (local-first approach)
    if use_ollama:
        try:
            # Try to use local Ollama server
            client = openai.OpenAI(
                base_url='http://localhost:11434/v1/',
                api_key='ollama',  # required but ignored for local Ollama
            )
            print("Using local Ollama for chat completions")
        except Exception as e:
            print(f"Ollama not available, falling back to OpenAI: {e}")
            use_ollama = False

    if not use_ollama:
        # Fallback to OpenAI
        openai.api_key = os.environ.get("OPENAI_API_KEY")
        if not openai.api_key:
            print("Warning: No OpenAI API key found. Please set OPENAI_API_KEY environment variable.")
            exit(1)
        client = openai.OpenAI()
        print("Using OpenAI for chat completions")

    character_prompt = 'Answer precise and short with the polite sarcasm of a butler.'

    # TTS Setup - prefer local options, fallback to Azure
    azure_key = os.environ.get("AZURE_SPEECH_KEY")
    azure_region = os.environ.get("AZURE_SPEECH_REGION")

    if azure_key and azure_region:
        stream = RealtimeTTS.TextToAudioStream(
            RealtimeTTS.AzureEngine(azure_key, azure_region),
            log_characters=True
        )
        print("Using Azure TTS")
    else:
        # Fallback to system TTS or other local options
        try:
            stream = RealtimeTTS.TextToAudioStream(
                RealtimeTTS.SystemEngine(),
                log_characters=True
            )
            print("Using System TTS (Azure keys not found)")
        except:
            print("Warning: No TTS engine available. Install RealtimeTTS and configure engines.")
            exit(1)

    # STT Setup - Use Ollama-enabled recorder with local-first approach
    recorder = RealtimeSTT.AudioToTextRecorder(
        model="gemma3:1b",  # Default Ollama model
        use_ollama=True,
        ollama_model="gemma3:1b",
        ollama_host="localhost:11434",
        fallback_to_external=True,  # Fallback to Whisper if Ollama unavailable
        whisper_fallback_model="medium"  # Fallback Whisper model
    )

    def generate(messages):
        model_name = "gemma3:1b" if use_ollama else "gpt-3.5-turbo"
        try:
            response = client.chat.completions.create(
                model=model_name,
                messages=messages,
                stream=True
            )
            for chunk in response:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
        except Exception as e:
            print(f"Error generating response: {e}")
            yield "Sorry, I encountered an error generating a response."

    print("Minimalistic Talkbot with Ollama Integration")
    print("=" * 50)
    print("Configuration:")
    print(f"  Chat Model: {'Ollama (gemma3:1b)' if use_ollama else 'OpenAI (gpt-3.5-turbo)'}")
    print(f"  STT: RealtimeSTT with Ollama integration")
    print(f"  TTS: {'Azure' if azure_key else 'System'}")
    print("=" * 50)

    history = []
    while True:
        print("\n\nSpeak when ready")
        print(f'>>> {(user_text := recorder.text())}\n<<< ', end="", flush=True)
        history.append({'role': 'user', 'content': user_text})
        assistant_response = generate([{ 'role': 'system',  'content': character_prompt}] + history[-10:])
        stream.feed(assistant_response).play()
        history.append({'role': 'assistant', 'content': stream.text()})