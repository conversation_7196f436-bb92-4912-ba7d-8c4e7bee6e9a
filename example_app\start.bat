@echo off
cd /d %~dp0

REM Check if the venv directory exists
if not exist test_env\Scripts\python.exe (
    echo Creating VENV
    python -m venv test_env
) else (
    echo VENV already exists
)


:: ============================================================================
:: RealtimeSTT Configuration - Local-First Approach with Ollama
:: ============================================================================

:: Ollama Configuration (Local AI - No API keys required!)
:: Install Ollama from https://ollama.ai and run: ollama pull gemma3:1b
set OLLAMA_HOST=localhost:11434
set OLLAMA_MODEL=gemma3:1b
set USE_OLLAMA=true

:: External API Keys (Optional - Only needed if Ollama is unavailable)
:: ============================================================================

:: OpenAI API Key  https://platform.openai.com/
set OPENAI_API_KEY=

:: Microsoft Azure API Key  https://portal.azure.com/
set AZURE_SPEECH_KEY=
set AZURE_SPEECH_REGION=

:: Elevenlabs API Key  https://www.elevenlabs.io/
set ELEVENLABS_API_KEY=


echo Activating VENV
start cmd /k "call test_env\Scripts\activate.bat && python ui_openai_voice_interface.py"